# Design Document

## Overview

NovaGame Mobile is a React Native application that brings the complex space simulation gameplay to mobile devices with a touch-optimized interface. The app will feature comprehensive resource management, fleet operations, galaxy exploration, and social features, all designed specifically for mobile interaction patterns. The design emphasizes offline-first capabilities, real-time synchronization, and multi-language support through react-i18next.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────┐
│     React Native + Tamagui UI       │
├─────────────────────────────────────┤
│   react-i18next Localization       │
├─────────────────────────────────────┤
│     Zustand State Management        │
├─────────────────────────────────────┤
│   Game Services + WebSocket API     │
├─────────────────────────────────────┤
│      MMKV Local Storage             │
└─────────────────────────────────────┘
```

### Technology Stack

- **Framework**: React Native (no Expo)
- **UI Library**: Tamagui for design system and performance
- **State Management**: Zustand for lightweight state management
- **Navigation**: React Navigation v6 with stack and tab navigators
- **Local Storage**: MMKV for high-performance data persistence
- **HTTP Client**: Axios with interceptors
- **Real-time**: WebSocket for live updates
- **Localization**: react-i18next for multi-language support
- **Package Manager**: pnpm
- **Testing**: Jest + React Native Testing Library + Detox

## Components and Interfaces

### Mobile-First Screen Design

#### 1. Main Dashboard (Mobile-Optimized)

- **Purpose**: Primary mobile interface with touch-optimized navigation
- **Mobile Layout**:
  - **Compact Header**: Resource counters with abbreviated formatting (2.65M, 2.95M, 39.1M)
  - **Player Badge**: Level indicator with tap-to-expand details
  - **Quick Actions**: Floating action buttons for common tasks
  - **Status Cards**: Swipeable cards showing planet status, fleet missions, construction progress
  - **Bottom Navigation**: Tab bar with main sections

#### 2. Slide-Out Navigation Menu

- **Purpose**: Complete game navigation optimized for mobile
- **Localized Menu Items**:
  - **t('navigation.overview')** - Empire overview
  - **t('navigation.buildings')** - Construction management
  - **t('navigation.planets')** - Planet management
  - **t('navigation.research')** - Technology tree
  - **t('navigation.shipyard')** - Ship construction
  - **t('navigation.fleet')** - Fleet management
  - **t('navigation.galaxy')** - Galaxy exploration
  - **t('navigation.defense')** - Defense systems
  - **t('navigation.alliance')** - Alliance features
  - **t('navigation.market')** - Trading system
  - **t('navigation.messages')** - Communication
  - **t('navigation.settings')** - Game settings

#### 3. Resource Production Screen (Mobile)

- **Purpose**: Touch-optimized resource management interface
- **Mobile Features**:
  - **Swipeable Tabs**: Production, Management, Modal tabs with swipe navigation
  - **Expandable Cards**: Tap to expand facility details
  - **Slider Controls**: Touch sliders for production level adjustment
  - **Quick Actions**: Swipe-to-upgrade gestures
  - **Production Facilities** (from GameVar.php):
    - Metal Mine (u1) - with production formulas
    - Crystal Mine (u2) - with efficiency calculations
    - Deuterium Synthesizer (u3) - temperature-dependent
    - Solar Plant (u4) - energy production
    - Fusion Reactor (u12) - deuterium consumption
    - Robotics Factory (u14) - construction speed

#### 4. Galaxy Exploration (Mobile)

- **Purpose**: Touch-friendly galaxy navigation and planet interaction
- **Mobile Interface**:
  - **Pinch-to-Zoom**: Galaxy grid with smooth zoom controls
  - **Planet Cards**: Tap planets to view details in bottom sheet
  - **Coordinate Input**: Number pad for quick coordinate jumping
  - **Activity Feed**: Slide-up panel showing recent player activities
  - **Filter Controls**: Horizontal scrolling filter chips

#### 5. Fleet Management (Mobile)

- **Purpose**: Mobile-optimized fleet operations and mission planning
- **Touch Features**:
  - **Ship Selection**: Multi-select with checkboxes and quantity steppers
  - **Mission Types** (from FlyingFleets.php):
    - Attack (1) - combat missions
    - ACS (2) - alliance combat system
    - Transport (3) - resource transport
    - Deploy (4) - fleet deployment
    - Spy (6) - reconnaissance missions
    - Colonize (7) - planet colonization
    - Recycle (8) - debris collection
    - Expedition (15) - exploration missions
  - **Destination Picker**: Galaxy coordinate selector with favorites
  - **Mission Timer**: Real-time countdown with push notifications

#### 6. Market Trading (Mobile)

- **Purpose**: Touch-optimized trading interface
- **Mobile Design**:
  - **Resource Tabs**: Horizontal tabs for Metal, Crystal, Deuterium, Dark Matter
  - **Order Book**: Scrollable list with pull-to-refresh
  - **Quick Trade**: Preset amount buttons (1K, 10K, 100K, Max)
  - **Price Charts**: Swipeable mini-charts showing price trends
  - **Trade History**: Expandable transaction history

#### 7. Technology Research (Mobile)

- **Purpose**: Mobile-friendly technology tree navigation
- **Touch Interface**:
  - **Zoomable Tree**: Pinch-to-zoom technology dependency tree
  - **Research Queue**: Drag-and-drop queue management
  - **Technology Cards**: Tap for detailed descriptions and requirements
  - **Progress Indicators**: Real-time research progress with notifications

### Shared Mobile Components

#### Navigation Components

- **DrawerMenu**: Slide-out navigation with gesture support
- **TabBar**: Bottom tab navigation with badges
- **HeaderBar**: Compact resource display with tap-to-expand

#### Touch-Optimized Components

- **ResourceCounter**: Animated counters with haptic feedback
- **ProductionSlider**: Touch sliders for production control
- **PlanetCard**: Swipeable planet information cards
- **FleetMissionCard**: Mission status with countdown timers
- **NotificationBadge**: Touch-friendly notification indicators
- **ActionSheet**: Bottom sheet for contextual actions

## Reference Files

The following reference files contain detailed game mechanics and implementation details:

- **#[[file:references/GameVar.php]]** - Core game configuration, resource definitions, building types, ship specifications, combat capabilities, and production formulas
- **#[[file:references/FlyingFleets.php]]** - Fleet mission logic, combat calculations, and fleet movement mechanics
- **#[[file:references/GameHelper.php]]** - Battle calculations, planet creation, and utility functions
- **#[[file:references/TechtreeSections.php]]** - Technology tree structure, building production calculations, and upgrade mechanics

## Data Models

### Game Entities (Based on Reference Files)

#### Resources Model

```typescript
type ResourcesType = {
  metal: number;
  crystal: number;
  deuterium: number;
  energy: number;
  darkMatter: number;
  storage: {
    metal: number;
    crystal: number;
    deuterium: number;
  };
};
```

#### Building Model (from RESOURCES mapping)

```typescript
type BuildingType = {
  id: string;
  type:
    | "u1"
    | "u2"
    | "u3"
    | "u4"
    | "u12"
    | "u14"
    | "u15"
    | "u21"
    | "u22"
    | "u23"
    | "u24"
    | "u31"
    | "u33"
    | "u34"
    | "u41"
    | "u42"
    | "u43"
    | "u44";
  level: number;
  planetId: string;
  isUnderConstruction: boolean;
  constructionEndTime?: Date;
  productionLevel: number; // 0-100%
};
```

#### Fleet Model (from COMBAT_CAPS)

```typescript
type FleetType = {
  id: string;
  ownerId: string;
  ships: {
    [key: string]: number; // ship type -> quantity
  };
  mission: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 15;
  startTime: Date;
  endTime: Date;
  startCoordinates: CoordinatesType;
  endCoordinates: CoordinatesType;
  resources: ResourcesType;
};
```

#### Research Model (from REQUIREMENTS)

```typescript
type ResearchType = {
  id: string;
  type:
    | "u106"
    | "u108"
    | "u109"
    | "u110"
    | "u111"
    | "u113"
    | "u114"
    | "u115"
    | "u117"
    | "u118"
    | "u120"
    | "u121"
    | "u122"
    | "u123"
    | "u124"
    | "u199";
  level: number;
  playerId: string;
  isResearching: boolean;
  researchEndTime?: Date;
  requirements: { [key: string]: number };
};
```

## Localization Strategy

### Language Support Structure

```typescript
// English (default)
const en = {
  navigation: {
    overview: "Overview",
    buildings: "Buildings",
    planets: "Planets",
    research: "Research",
    shipyard: "Shipyard",
    fleet: "Fleet",
    galaxy: "Galaxy",
    defense: "Defense",
    alliance: "Alliance",
    market: "Market",
    messages: "Messages",
    settings: "Settings",
  },
  resources: {
    metal: "Metal",
    crystal: "Crystal",
    deuterium: "Deuterium",
    energy: "Energy",
    darkMatter: "Dark Matter",
  },
  buildings: {
    metal_mine: "Metal Mine",
    crystal_mine: "Crystal Mine",
    deuterium_sintetizer: "Deuterium Synthesizer",
    solar_plant: "Solar Plant",
    fusion_plant: "Fusion Reactor",
    robot_factory: "Robotics Factory",
  },
  missions: {
    attack: "Attack",
    acs: "Alliance Combat",
    transport: "Transport",
    deploy: "Deploy",
    spy: "Espionage",
    colonize: "Colonize",
    recycle: "Recycle",
    expedition: "Expedition",
  },
};
```

### Dynamic Content Localization

- **Number Formatting**: Locale-aware number formatting (1,234,567 vs 1.234.567)
- **Time Display**: Localized time and date formats
- **Resource Abbreviations**: Language-specific abbreviations (1.2M vs 1,2M)
- **Pluralization**: Proper plural forms for different languages

## Error Handling

### Mobile-Specific Error Management

- **Network Connectivity**: Detect and handle offline/online state changes
- **Touch Feedback**: Haptic feedback for errors and confirmations
- **Toast Notifications**: Non-intrusive error messages
- **Retry Mechanisms**: Pull-to-refresh and retry buttons

### Game Logic Validation

- **Resource Validation**: Client-side validation with server confirmation
- **Mission Validation**: Fleet availability and travel time checks
- **Construction Validation**: Building requirements and resource availability

## Testing Strategy

### Mobile Testing Approach

- **Device Testing**: Test on various screen sizes and orientations
- **Gesture Testing**: Validate swipe, pinch, and tap interactions
- **Performance Testing**: Memory usage and battery consumption
- **Offline Testing**: Validate offline functionality and sync

### Game Logic Testing

- **Resource Calculations**: Test production formulas from PROD_GRID
- **Combat Simulation**: Validate battle calculations from COMBAT_CAPS
- **Mission Logic**: Test fleet mission mechanics from FlyingFleets.php

## UI/UX Design Principles

### Mobile-First Design

- **Touch Targets**: Minimum 44px touch targets with adequate spacing
- **Thumb Navigation**: Important actions within thumb reach zones
- **Gesture Support**:
  - Swipe between tabs and screens
  - Pinch-to-zoom for galaxy and tech tree
  - Pull-to-refresh for data updates
  - Long-press for context menus

### Visual Design System

- **Dark Space Theme**:
  - Primary: #0a0a0a (deep space black)
  - Secondary: #1a1a2e (dark blue-gray)
  - Accent: #00d4ff (cyan blue)
  - Success: #00ff88 (green)
  - Warning: #ffaa00 (orange)
  - Error: #ff4444 (red)
- **Typography**: Scalable fonts that respect system accessibility settings
- **Animations**: 60fps smooth animations with reduced motion support
- **Haptic Feedback**: Contextual vibration for important actions

### Accessibility Features

- **Screen Reader**: Full VoiceOver/TalkBack support
- **High Contrast**: Alternative color schemes for visibility
- **Font Scaling**: Support for system font size preferences
- **Reduced Motion**: Honor accessibility motion preferences
- **Color Blind Support**: Ensure information isn't color-dependent only
