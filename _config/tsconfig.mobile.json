// 23.08.2025 09:50
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    // React Native Environment
    "target": "ES2020", // React Native supports ES2020
    "lib": ["ES2020"], // No DOM for mobile, just ES features

    // Module System - React Native specific
    "module": "ESNext",
    "moduleResolution": "bundler",

    // React Native JSX
    "jsx": "react-native",
    "jsxImportSource": "react",

    // Mobile-specific optimizations
    "noEmit": true, // React Native bundler handles emission
    "verbatimModuleSyntax": false, // Allow flexible import/export for Metro

    // React Native features
    "useDefineForClassFields": true,
    "allowImportingTsExtensions": false,

    // Types for React Native environment (Jest instead of Vitest)
    "types": ["node", "jest"],

    // Performance optimizations for React Native
    "assumeChangesOnlyAffectDirectDependencies": true,
    "skipLibCheck": true // Important for React Native to avoid lib conflicts
  },
  "include": [
    "src/**/*",
    "**/*.tsx",
    "**/*.ts",
    "index.js", // Common RN entry point
    "metro.config.js",
    "babel.config.js"
  ],
  "exclude": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/*.test.*",
    "**/*.spec.*",
    "**/coverage/**",
    "**/*.config.js",
    "**/*.config.ts",
    "**/web/**", // Exclude web-specific code
    "**/public/**", // Exclude web public assets
    "**/android/build/**", // Exclude Android build artifacts
    "**/ios/build/**", // Exclude iOS build artifacts
    "**/android/app/build/**"
  ]
}
