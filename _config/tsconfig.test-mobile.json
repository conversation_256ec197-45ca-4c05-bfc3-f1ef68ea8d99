// 23.08.2025 09:50
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "lib": ["ES2022"],
    "target": "ES2022",

    "module": "ESNext",
    "moduleResolution": "node",
    "jsx": "react-native",

    "types": [
      "node",
      "jest",
      "@types/react-native",
      "@types/react",
      "@types/react-test-renderer"
    ],
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,

    "noEmit": true,
    "sourceMap": true,

    "strict": false,
    "noImplicitAny": false,
    "useUnknownInCatchVariables": false,
    "exactOptionalPropertyTypes": false,
    "noUncheckedIndexedAccess": false,
    "noImplicitOverride": false,
    "noPropertyAccessFromIndexSignature": false,

    "isolatedModules": true,
    "skipLibCheck": true,

    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "noImplicitReturns": false
  },
  "include": [
    // Mobile-specific test patterns
    "../**/*.(test|spec).(native|rn|mobile).ts",
    "../**/*.(test|spec).(native|rn|mobile).tsx",
    "../**/react-native/**/__tests__/**/*.ts",
    "../**/react-native/**/__tests__/**/*.tsx",
    "../**/mobile/**/__tests__/**/*.ts",
    "../**/mobile/**/__tests__/**/*.tsx",
    "../rn/**/__tests__/**/*.ts",
    "../rn/**/__tests__/**/*.tsx",
    "../rn/**/*.test.ts",
    "../rn/**/*.test.tsx",
    "../**/__mocks__/**/*.ts",
    "../**/__mocks__/**/*.tsx"
  ],
  "exclude": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/coverage/**",
    "**/android/**",
    "**/ios/**",
    "**/.expo/**"
  ]
}
