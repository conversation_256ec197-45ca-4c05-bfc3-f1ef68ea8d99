const { getDefaultConfig } = require('@expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Configure path mapping for @ imports
config.resolver.alias = {
  '@': path.resolve(__dirname, 'src'),
  '@components': path.resolve(__dirname, 'src/components'),
  '@constants': path.resolve(__dirname, 'src/constants'),
  '@services': path.resolve(__dirname, 'src/services'),
  '@utils': path.resolve(__dirname, 'src/utils'),
  '@types': path.resolve(__dirname, 'src/types'),
  '@stores': path.resolve(__dirname, 'src/stores'),
};

// Optimize bundle size to prevent OOM errors
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    mangle: {
      keep_fnames: true,
    },
    output: {
      ascii_only: true,
      quote_style: 3,
      wrap_iife: true,
    },
    sourceMap: false,
    toplevel: false,
    warnings: false,
    parse: {
      bare_returns: false,
    },
  },
};

// Reduce memory usage during bundling
config.serializer = {
  ...config.serializer,
  createModuleIdFactory: () => (path) => {
    // Use shorter module IDs to reduce bundle size
    return path.replace(/.*\/node_modules\//, '').replace(/\//g, '-');
  },
};

module.exports = config;
