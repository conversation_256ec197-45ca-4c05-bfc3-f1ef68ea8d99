/**
 * Global error boundary component for catching React errors
 */

import React, { Component, type ReactNode, type ErrorInfo } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { Button } from '../../design-system/components/Button';
import { errorHandler } from '../../services/error/errorHandler';
import type { GameError } from '../../types/error';

type ErrorBoundaryProps = {
	children: ReactNode;
	fallback?: (error: GameError, retry: () => void) => ReactNode;
	onError?: (error: GameError) => void;
};

type ErrorBoundaryState = {
	hasError: boolean;
	error: GameError | null;
	errorId: string | null;
};

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState>
{
	constructor(props: ErrorBoundaryProps)
	{
		super(props);

		this.state = {
			hasError: false,
			error: null,
			errorId: null,
		};
	}

	static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState>
	{
		return {
			hasError: true,
		};
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo): void
	{
		const gameError: GameError = {
			id: `boundary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			code: 'REACT_ERROR_BOUNDARY',
			message: error.message,
			category: 'ui',
			severity: 'high',
			context: {
				timestamp: new Date(),
				screen: 'unknown',
				action: 'component_render',
				metadata: {
					componentStack: errorInfo.componentStack,
					errorBoundary: true,
				},
			},
			stack: error.stack,
			originalError: error,
			retryable: true,
			userMessage: 'Something went wrong with the interface. Please try again.',
			suggestedActions: ['Retry', 'Refresh app', 'Contact support'],
		};

		this.setState({
			error: gameError,
			errorId: gameError.id,
		});

		// Handle the error through our error handler
		errorHandler.handleError(gameError);

		// Call custom error handler if provided
		if (this.props.onError)
		{
			this.props.onError(gameError);
		}
	}

	private handleRetry = (): void => {
		this.setState({
			hasError: false,
			error: null,
			errorId: null,
		});
	};

	private handleReportError = (): void => {
		if (this.state.error)
		{
			// This would open a bug report screen or send to support
			console.log('Reporting error:', this.state.error);
		}
	};

	render(): ReactNode
	{
		if (this.state.hasError && this.state.error)
		{
			// Use custom fallback if provided
			if (this.props.fallback)
			{
				return this.props.fallback(this.state.error, this.handleRetry);
			}

			// Default error UI
			return (
				<View style={styles.container}>
					<View style={styles.content}>
						<Text style={styles.title}>Oops! Something went wrong</Text>
						<Text style={styles.message}>
							{this.state.error.userMessage || 'An unexpected error occurred'}
						</Text>

						<View style={styles.actions}>
							<Button
								title="Try Again"
								onPress={this.handleRetry}
								variant="primary"
								style={styles.button}
							/>
							<Button
								title="Report Issue"
								onPress={this.handleReportError}
								variant="secondary"
								style={styles.button}
							/>
						</View>

						{__DEV__ && (
							<ScrollView style={styles.debugInfo}>
								<Text style={styles.debugTitle}>Debug Information:</Text>
								<Text style={styles.debugText}>
									Error: {this.state.error.message}
								</Text>
								<Text style={styles.debugText}>
									Code: {this.state.error.code}
								</Text>
								<Text style={styles.debugText}>
									Category: {this.state.error.category}
								</Text>
								{this.state.error.stack && (
									<Text style={styles.debugText}>
										Stack: {this.state.error.stack}
									</Text>
								)}
							</ScrollView>
						)}
					</View>
				</View>
			);
		}

		return this.props.children;
	}
}

const styles = {
	container: {
		flex: 1,
		backgroundColor: '#f5f5f5',
		justifyContent: 'center' as const,
		alignItems: 'center' as const,
		padding: 20,
	},
	content: {
		backgroundColor: 'white',
		borderRadius: 8,
		padding: 24,
		maxWidth: 400,
		width: '100%',
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.1,
		shadowRadius: 4,
		elevation: 3,
	},
	title: {
		fontSize: 20,
		fontWeight: 'bold' as const,
		color: '#333',
		textAlign: 'center' as const,
		marginBottom: 12,
	},
	message: {
		fontSize: 16,
		color: '#666',
		textAlign: 'center' as const,
		marginBottom: 24,
		lineHeight: 22,
	},
	actions: {
		flexDirection: 'row' as const,
		justifyContent: 'space-between' as const,
		gap: 12,
	},
	button: {
		flex: 1,
	},
	debugInfo: {
		marginTop: 24,
		maxHeight: 200,
		backgroundColor: '#f8f8f8',
		padding: 12,
		borderRadius: 4,
	},
	debugTitle: {
		fontSize: 14,
		fontWeight: 'bold' as const,
		color: '#333',
		marginBottom: 8,
	},
	debugText: {
		fontSize: 12,
		color: '#666',
		fontFamily: 'monospace',
		marginBottom: 4,
	},
};

export { ErrorBoundary };
export type { ErrorBoundaryProps };
