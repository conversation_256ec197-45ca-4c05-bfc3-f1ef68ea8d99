import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Platform } from 'react-native';

// TEMPORARY: Minimal imports to test memory issues
// import { RootNavigator } from './src/navigation';
// import { initI18n } from './src/localization';
// import AccessibilityManager from './src/utils/accessibility';
// import { initializeMockData } from './src/utils/initializeMockData';
// import { initializeBackgroundTasks, cleanupBackgroundTasks } from './src/services/background';
// import { ErrorBoundary } from './src/components/error/ErrorBoundary';

export default function App(): React.JSX.Element {
	console.log('🚀 MINIMAL APP STARTING...');
	console.log('📱 Platform:', Platform.OS);
	console.log('🔧 Environment:', __DEV__ ? 'development' : 'production');

	// TEMPORARY: Ultra-minimal app to test memory issues
	return (
		<SafeAreaProvider>
			<View style={styles.container}>
				<StatusBar style="light" backgroundColor="#0a0a0a" />
				<Text style={styles.text}>🎮 NovaGame Mobile</Text>
				<Text style={styles.subtext}>Minimal Test Version</Text>
				<Text style={styles.debug}>Platform: {Platform.OS}</Text>
				<Text style={styles.debug}>Environment: {__DEV__ ? 'development' : 'production'}</Text>
			</View>
		</SafeAreaProvider>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#0a0a0a',
		justifyContent: 'center',
		alignItems: 'center',
		padding: 20,
	},
	text: {
		fontSize: 24,
		fontWeight: 'bold',
		color: '#ffffff',
		marginBottom: 10,
	},
	subtext: {
		fontSize: 16,
		color: '#00d4ff',
		marginBottom: 20,
	},
	debug: {
		fontSize: 12,
		color: '#888888',
		marginBottom: 5,
	},
});
