import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Platform } from 'react-native';
import 'react-native-gesture-handler';

import { RootNavigator } from './src/navigation';
import { initI18n } from './src/localization';
import AccessibilityManager from './src/utils/accessibility';
import { GestureProvider } from './src/components/gestures';
import { DesignSystemProvider } from './src/design-system';
import { initializeMockData } from './src/utils/initializeMockData';
import { RealtimeSyncProvider } from './src/components/realtime/RealtimeSyncProvider';
import { initializeBackgroundTasks, cleanupBackgroundTasks } from './src/services/background';
import { ErrorBoundary } from './src/components/error/ErrorBoundary';

export default function App(): React.JSX.Element {
	const [isAppInitialized, setIsAppInitialized] = useState(false);

	useEffect(() => {
		const initializeApp = async () => {
			try {
				console.log('🚀 Starting app initialization...');
				console.log('📱 Platform:', Platform.OS);
				console.log('🔧 Environment:', __DEV__ ? 'development' : 'production');

				// Initialize localization first (most stable)
				try {
					console.log('🌐 Initializing localization...');
					await initI18n();
					console.log('✅ Localization initialized successfully');
				} catch (error) {
					console.error('❌ Localization initialization failed:', error);
					console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
				}

				// Initialize accessibility manager
				try {
					console.log('♿ Initializing accessibility manager...');
					await AccessibilityManager.initialize();
					console.log('✅ Accessibility manager initialized successfully');
				} catch (error) {
					console.error('❌ Accessibility manager initialization failed:', error);
					console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
				}

				// Initialize mock data for development
				try {
					console.log('🎮 Initializing mock data...');
					await initializeMockData();
					console.log('✅ Mock data initialized successfully');
				} catch (error) {
					console.error('❌ Mock data initialization failed:', error);
					console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
				}

				// Initialize background task system (optional)
				try {
					console.log('⚙️ Initializing background tasks...');
					await initializeBackgroundTasks();
					console.log('✅ Background tasks initialized successfully');
				} catch (error) {
					console.error('❌ Background tasks initialization failed:', error);
					console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
				}

				console.log('🎉 App initialization complete - setting app as ready');
				setIsAppInitialized(true);
			}
			catch (error) {
				const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
				const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
				console.error('💥 CRITICAL app initialization failure:', errorMessage);
				console.error('💥 Error stack:', errorStack);
				console.error('💥 Error object:', error);

				// Still set as initialized to prevent infinite loading, but log it clearly
				console.warn('⚠️ Setting app as initialized despite errors to prevent infinite loading');
				setIsAppInitialized(true);
			}
		};

		initializeApp();

		// Cleanup on unmount
		return () => {
			try {
				AccessibilityManager.cleanup();
				cleanupBackgroundTasks();
			} catch (error) {
				console.warn('Cleanup error:', error);
			}
		};
	}, []);

	if (!isAppInitialized) {
		console.log('⏳ App is initializing...');
		return <></>; // Or a loading screen
	}

	// TEMPORARY: Minimal app structure to test memory issues
	return (
		<ErrorBoundary>
			<SafeAreaProvider>
				<StatusBar style="light" backgroundColor="#0a0a0a" />
				<RootNavigator />
			</SafeAreaProvider>
		</ErrorBoundary>
	);
}
